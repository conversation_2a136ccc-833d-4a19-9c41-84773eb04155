<script setup>
import { useGlobalData } from '@/store/global'
import ApiService from '@/services/ApiService'
import { isNumber } from '@/utils/helpers'
import { vMaska } from 'maska'
import { isEmpty } from '@/@core/utils'

const globalData = useGlobalData()
const { showSnackbar } = globalData

const loading = ref(null)
const skeletonLoading = ref(true)
const physicianFee = ref(null)
const physicianFeeTreatments = ref(null)
const firstOrderDiscount = ref(null)
const minAmountAllowedToWithdraw = ref(null)
const documentUploadStage = ref('after') // after | before
const guestCheckoutStatus = ref(true)

const otcShippingFeeStatus = ref(true)
const otcShippingFeeAmount = ref(null)
const otcShippingFeeThreshold = ref(null)

const treatmentOptions = [
  { title: 'Erectile Dysfunction (ED)', value: 'ED' },
  { title: 'Hair Loss', value: 'HL' },
  { title: 'Weight Loss', value: 'WL' },
]

onMounted(async () => {
  skeletonLoading.value = true

  const data = await fetchSetting()

  physicianFee.value = data?.consultation_fee ?? null
  physicianFeeTreatments.value = data?.treatments ?? null
  firstOrderDiscount.value = data?.first_order_discount_amount ?? null
  minAmountAllowedToWithdraw.value = data?.minimum_withdraw_amount ?? null
  documentUploadStage.value = data?.document_uploaded_at_checkout_stage ?? 'after'
  guestCheckoutStatus.value = Boolean(data?.is_guest_checkout_status)

  otcShippingFeeStatus.value = Boolean(data?.otc_order_shipping_fee_enabled)
  otcShippingFeeAmount.value = data?.otc_order_shipping_fee_amount
  otcShippingFeeThreshold.value = data?.otc_order_shipping_fee_threshold

  skeletonLoading.value = false
})

/**
 * Fetch fee detail
 * @param {string} settingType The type of fee. Accepted values are:
 *   provider_consultation_fee, first_order_discount_amount, local_pharmacy_status,minimum_withdraw_amount,document_uploaded_at_checkout_stage
 * @returns {object|null} The fee detail object | null
 */
async function fetchSetting(settingType = null) {
  try {
    let requestUrl = '/admin/global-settings'
    if (settingType) requestUrl += `/${settingType}`
    const { data } = await ApiService.get(requestUrl)

    if (data.status === 200) {
      return data.info
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch(error) {
    console.error(error)
  }

  return null
}

const updatePhysicianFee = async () => {
  try {
    loading.value = 'consult_fee'

    const { data } = await ApiService.post('/admin/update-beluga-consultation-fee', {
      consultation_fee: physicianFee.value,
      treatments: physicianFeeTreatments.value,
    })

    if (data.status === 200) {
      showSnackbar(data.message)
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch(error) {
    console.error(error)
  } finally {
    loading.value = null
  }
}

const updateFirstOrderDiscount = async () => {
  try {
    loading.value = 'first_order_discount'

    const { data } = await ApiService.post('/admin/update-first-order-discount-amount', {
      first_order_discount_amount: firstOrderDiscount.value,
    })

    if (data.status === 200) {
      showSnackbar(data.message)
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch(error) {
    console.error(error)
  } finally {
    loading.value = null
  }
}

const updateMinAmountAllowedToWithdraw = async () => {
  try {
    loading.value = 'min_amount_withdraw'

    const { data } = await ApiService.post('/admin/update-minimum-withdraw-fee', {
      minimum_withdraw_amount: minAmountAllowedToWithdraw.value,
    })

    if (data.status === 200) {
      showSnackbar(data.message)
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch(error) {
    console.error(error)
  } finally {
    loading.value = null
  }
}

const updateDocumentUploadStage = async () => {
  try {
    loading.value = 'document_upload_stage'

    const { data } = await ApiService.post('/admin/update-document-uploaded_stage', {
      document_uploaded_at_checkout_stage: documentUploadStage.value,
    })

    if (data.status === 200) {
      showSnackbar(data.message)
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch(error) {
    console.error(error)
  } finally {
    loading.value = null
  }
}

const updateGuestCheckoutStatus = async () => {
  try {
    loading.value = 'guest_checkout'

    const { data } = await ApiService.post('/admin/update-guest-checkout-status')

    if (data.status === 200) {
      showSnackbar(data.message)
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch(error) {
    console.error(error)
  } finally {
    loading.value = null
  }
}

const updateOtcShippingFee = async () => {
  try {
    loading.value = 'otc_shipping_fee'

    const { data } = await ApiService.post('/admin/update-otc-order-shipping-cost', {
      'otc_order_shipping_fee_enabled': otcShippingFeeStatus.value ? 1 : 0,
      'otc_order_shipping_fee_threshold': otcShippingFeeThreshold.value,
      'otc_order_shipping_fee_amount': otcShippingFeeAmount.value,
    })

    if (data.status === 200) {
      showSnackbar(data.message)
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch(error) {
    console.error(error)
  } finally {
    loading.value = null
  }
}
</script>

<template>
  <VRow>
    <!-- Consultation Fees -->
    <VCol cols="12">
      <VCard
        title="Consultation fee"
        subtitle="This fees will be charged for every visit sent to Provider."
      >
        <VCardText v-if="skeletonLoading">
          <VRow>
            <VCol
              cols="12"
              sm="6"
            >
              <Skeleton height="2rem" />
            </VCol>
            <VCol cols="12">
              <Skeleton
                height="2rem"
                width="6rem"
              />
            </VCol>
          </VRow>
        </VCardText>
        <VCardText v-else>
          <VRow>
            <VCol
              cols="12"
              sm="6"
            >
              <VTextField
                v-model="physicianFee"
                label="Consultation fee"
                prefix="$"
                type="text"
                @keypress="isNumber($event)"
              />
              <input
                v-model="physicianFee"
                v-maska
                type="hidden"
                class="d-none"
                data-maska="##.#####"
              >
            </VCol>
            <VCol cols="12">
              <div>
                <label class="d-block mb-2">
                  Select treatments where this consultation fee is applicable:
                </label>
                <div
                  v-for="item in treatmentOptions"
                  :key="item.value"
                  style="min-width: 220px;"
                >
                  <VCheckbox
                    v-model="physicianFeeTreatments"
                    :label="item.title"
                    :value="item.value"
                    class="me-3"
                  />
                </div>
              </div>
            </VCol>
            <VCol cols="12">
              <VBtn
                :loading="loading === 'consult_fee'"
                :disabled="!isEmpty(loading)"
                @click="updatePhysicianFee"
              >
                Submit
              </VBtn>
            </VCol>
          </VRow>
        </VCardText>
      </VCard>
    </VCol>

    <!-- First Order Discount -->
    <VCol cols="12">
      <VCard
        title="First Order Discount"
        subtitle="This amount will be discounted on the first order of a new user."
      >
        <VCardText v-if="skeletonLoading">
          <VRow>
            <VCol
              cols="12"
              sm="6"
            >
              <Skeleton height="2rem" />
            </VCol>
            <VCol cols="12">
              <Skeleton
                height="2rem"
                width="6rem"
              />
            </VCol>
          </VRow>
        </VCardText>
        <VCardText v-else>
          <VRow>
            <VCol
              cols="12"
              sm="6"
            >
              <VTextField
                v-model="firstOrderDiscount"
                label="First Order Discount"
                prefix="$"
                type="text"
                @keypress="isNumber($event)"
              />
              <input
                v-model="firstOrderDiscount"
                v-maska
                type="hidden"
                class="d-none"
                data-maska="###.#####"
              >
            </VCol>
            <VCol cols="12">
              <VBtn
                :loading="loading === 'first_order_discount'"
                :disabled="!isEmpty(loading)"
                @click="updateFirstOrderDiscount"
              >
                Submit
              </VBtn>
            </VCol>
          </VRow>
        </VCardText>
      </VCard>
    </VCol>

    <!-- Minimum Amount Allowed To Withdraw -->
    <VCol cols="12">
      <VCard
        title="Affiliate - Minimum Balance Required for Payout"
        subtitle="This is the minimum balance an affiliate must have in their wallet to request a payout."
      >
        <VCardText v-if="skeletonLoading">
          <VRow>
            <VCol
              cols="12"
              sm="6"
            >
              <Skeleton height="2rem" />
            </VCol>
            <VCol cols="12">
              <Skeleton
                height="2rem"
                width="6rem"
              />
            </VCol>
          </VRow>
        </VCardText>
        <VCardText v-else>
          <VRow>
            <VCol
              cols="12"
              sm="6"
            >
              <VTextField
                v-model="minAmountAllowedToWithdraw"
                label="Minimum Amount Threshold for Withdrawal"
                prefix="$"
                type="text"
                @keypress="isNumber($event)"
              />
              <input
                v-model="minAmountAllowedToWithdraw"
                v-maska
                type="hidden"
                class="d-none"
                data-maska="###.#####"
              >
            </VCol>
            <VCol cols="12">
              <VBtn
                :loading="loading === 'min_amount_withdraw'"
                :disabled="!isEmpty(loading)"
                @click="updateMinAmountAllowedToWithdraw"
              >
                Submit
              </VBtn>
            </VCol>
          </VRow>
        </VCardText>
      </VCard>
    </VCol>

    <!-- Document upload stage -->
    <VCol cols="12">
      <VCard
        title="Document Upload Stage"
        subtitle="This setting handles when to prompt user for document upload during order flow."
      >
        <VCardText v-if="skeletonLoading">
          <VRow>
            <VCol
              cols="12"
              sm="6"
            >
              <Skeleton height="2rem" />
            </VCol>
            <VCol cols="12">
              <Skeleton
                height="2rem"
                width="6rem"
              />
            </VCol>
          </VRow>
        </VCardText>
        <VCardText v-else>
          <VRow>
            <VCol
              cols="12"
              sm="6"
            >
              <VRadioGroup
                v-model="documentUploadStage"
                inline
              >
                <VRadio
                  label="After Checkout"
                  value="after"
                  class="ms-2"
                />
                <VRadio
                  label="Before Checkout"
                  value="before"
                  class="ms-2"
                />
              </VRadioGroup>
            </VCol>
            <VCol cols="12">
              <VBtn
                :loading="loading === 'document_upload_stage'"
                :disabled="!isEmpty(loading)"
                @click="updateDocumentUploadStage"
              >
                Submit
              </VBtn>
            </VCol>
          </VRow>
        </VCardText>
      </VCard>
    </VCol>

    <!-- Guest Checkout Status -->
    <VCol cols="12">
      <VCard
        title="Guest Checkout"
        subtitle="This setting handles if the guest checkout is enabled for OTC products."
      >
        <VCardText v-if="skeletonLoading">
          <VRow>
            <VCol
              cols="12"
              sm="6"
            >
              <Skeleton height="2rem" />
            </VCol>
            <VCol cols="12">
              <Skeleton
                height="2rem"
                width="6rem"
              />
            </VCol>
          </VRow>
        </VCardText>
        <VCardText v-else>
          <VRow>
            <VCol
              cols="12"
              sm="6"
            >
              <VSwitch
                v-model="guestCheckoutStatus"
                inset
                :label="guestCheckoutStatus ? 'Enabled' : 'Disabled'"
                style="width: fit-content;"
                :loading="loading === 'guest_checkout'"
                @change="updateGuestCheckoutStatus"
              />
            </VCol>
          </VRow>
        </VCardText>
      </VCard>
    </VCol>

    <!-- OTC Shipping Fee -->
    <VCol cols="12">
      <VCard
        title="OTC Shipping Fee"
        subtitle="This setting sets the shipping fee for OTC orders."
      >
        <VCardText v-if="skeletonLoading">
          <VRow>
            <VCol
              cols="12"
              sm="6"
            >
              <Skeleton height="2rem" />
            </VCol>
            <VCol cols="12">
              <Skeleton
                height="2rem"
                width="6rem"
              />
            </VCol>
          </VRow>
        </VCardText>
        <VCardText v-else>
          <VRow>
            <VCol
              cols="12"
              class="mb-3"
            >
              <VSwitch
                v-model="otcShippingFeeStatus"
                inset
                :label="otcShippingFeeStatus ? 'Enabled' : 'Disabled'"
                style="width: fit-content;"
                :loading="loading === 'otc_shipping_fee'"
              />
            </VCol>

            <VCol
              v-if="otcShippingFeeStatus"
              cols="12"
              sm="6"
            >
              <VTextField
                v-model="otcShippingFeeAmount"
                label="Shipping Fee Amount"
                prefix="$"
                type="text"
                @keypress="isNumber($event)"
              />
              <input
                v-model="otcShippingFeeAmount"
                v-maska
                type="hidden"
                class="d-none"
                data-maska="###.#####"
              >
            </VCol>
            <VCol
              v-if="otcShippingFeeStatus"
              cols="12"
              sm="6"
            >
              <VTextField
                v-model="otcShippingFeeThreshold"
                label="Shipping Fee Threshold"
                prefix="$"
                type="text"
                @keypress="isNumber($event)"
              />
              <input
                v-model="otcShippingFeeThreshold"
                v-maska
                type="hidden"
                class="d-none"
                data-maska="###.#####"
              >
            </VCol>

            <VCol cols="12">
              <VBtn
                :loading="loading === 'otc_shipping_fee'"
                :disabled="!isEmpty(loading)"
                @click="updateOtcShippingFee"
              >
                Submit
              </VBtn>
            </VCol>
          </VRow>
        </VCardText>
      </VCard>
    </VCol>
  </VRow>
</template>
