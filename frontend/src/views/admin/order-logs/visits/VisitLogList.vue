<script setup>
import { isEmpty } from '@/@core/utils'
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { resolveVisitStatus } from '@/utils/admin'
import { processErrors } from '@/utils/errorHandler'
import { calculateStartIndex, formattedPhoneNumber, resolveInitials } from '@/utils/helpers'
import { refDebounced } from '@vueuse/core'
import { computed, onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const globalData = useGlobalData()
const { showSnackbar } = globalData
const route = useRoute()
const router = useRouter()

const searchQuery = ref(route.query.search ?? null)
const debouncedSearchQuery = refDebounced(searchQuery, 300)
const filterIsSentToWhiteLblRx = ref(!isNaN(parseInt(route.query.is_sent_to_whitelblrx)) ? parseInt(route.query.is_sent_to_whitelblrx) : null)
const filterVisitType = ref(route.query.visit_type ?? null)
const dateRange = ref(route.query.from_date && route.query.to_date ? route.query.from_date + ' to ' + route.query.to_date : null)
const fromDate = ref(null)
const toDate = ref(null)
const rowPerPage = ref(10)
const currentPage = ref(!isNaN(parseInt(route.query.page)) ? parseInt(route.query.page) : 1)
const totalPage = ref(1)
const totalItems = ref(0)
const items = ref([])
const sortByColumnName = ref(route.query.sort_by ?? 'created_at')
const sortDirection = ref(route.query.sort_dir ?? 'desc')
const skeletonLoading = ref(true)
const visitTypes = ref([])

onMounted(() => {
  fetchItems()
})

const updateRoute = () => {
  router.replace({
    query: {
      page: currentPage.value,
      per_page: rowPerPage.value,
      search: searchQuery.value,
      sort_by: sortByColumnName.value,
      sort_dir: sortDirection.value,
      from_date: fromDate.value,
      to_date: toDate.value,
      is_sent_to_whitelblrx: filterIsSentToWhiteLblRx.value,
      visit_type: filterVisitType.value,
    },
  })
}

watch(dateRange, () => {
  if (isEmpty(dateRange.value)) {
    fromDate.value = null
    toDate.value = null

    return
  }

  const dateArr = dateRange.value.split(' to ')

  if (dateArr.length === 1) {
    fromDate.value = dateArr[0] || null
    toDate.value = dateArr[0] || null
  } else if (dateArr.length === 2) {
    fromDate.value = dateArr[0] || null
    toDate.value = dateArr[1] || null
  } else {
    fromDate.value = null
    toDate.value = null
  }
})

watch([debouncedSearchQuery, fromDate, toDate, rowPerPage, filterVisitType, filterIsSentToWhiteLblRx], () => {
  currentPage.value = 1
  fetchItems()
})

watch([currentPage, sortByColumnName, sortDirection], () => {
  fetchItems()
})

const fetchItems = async () => {
  skeletonLoading.value = true

  try {
    const { data } = await ApiService.post('/admin/visit-list', {
      searchQuery: searchQuery.value,
      perPage: rowPerPage.value,
      page: currentPage.value,
      sortByColumnName: sortByColumnName.value,
      isSortDirDesc: sortDirection.value,
      from_date: fromDate.value,
      to_date: toDate.value,
      is_sent_to_whitelblrx: filterIsSentToWhiteLblRx.value,
      visit_type: filterVisitType.value,
    })

    if (data.status === 200) {
      const pagedData = data.belugaOrders
      let startIndex = calculateStartIndex(pagedData.current_page, pagedData.per_page)
      if (currentPage.value > 1) {
        startIndex = (currentPage.value - 1) * rowPerPage.value + 1
      }

      items.value = pagedData.records.map((d, index) => ({
        ...d,
        sno: index + startIndex,
      }))
      totalPage.value = pagedData.totalPage == 0 ? 1 : pagedData.totalPage
      totalItems.value = pagedData.totalRecords
      updateRoute()

      // init options
      visitTypes.value = data.belugaVisitTypes
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    console.error(error)
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    skeletonLoading.value = false
  }
}

const sortColumn = columnName => {
  if (sortByColumnName.value !== '' && sortByColumnName.value === columnName && sortDirection.value === 'asc') {
    sortDirection.value = 'desc'
  } else {
    sortDirection.value = 'asc'
  }

  sortByColumnName.value = columnName
}

// 👉 Computing pagination data
const paginationData = computed(() => {
  const currentPageValue = currentPage.value <= 0 ? 1 : currentPage.value

  const firstIndex = items.value.length
    ? (currentPageValue - 1) * rowPerPage.value + 1
    : 0

  const lastIndex = items.value.length + (currentPageValue - 1) * rowPerPage.value

  return `Showing ${firstIndex} to ${lastIndex} of ${totalItems.value} entries`
})

function resolveRedirectLink(item) {
  const visitType = item.visit_type.toLowerCase()

  const subscriptionDetailsRouteMap = {
    'ed': 'admin-ed-manage-subscription',
    'hairloss': 'admin-hl-manage-subscription',
    'weightloss': 'admin-wl-manage-subscription',
    'weightlossfollowup': 'admin-wl-manage-subscription',
  }

  const orderDetailsRouteMap = {
    'ed': 'admin-ed-order-details',
    'hairloss': 'admin-hl-order-details',
  }

  if (item.is_medicine_pickup_at_local_pharmacy === 1) {
    return {
      name: orderDetailsRouteMap[visitType],
      params: { orderId: item.subscription_refill_id },
      query: { ...route.query },
    }
  } else {
    return {
      name: subscriptionDetailsRouteMap[visitType],
      params: { subscriptionId: item.subscription_id },
      query: { ...route.query },
    }
  }
}

const isRetryToBelugaDialogVisible = ref(false)
const retryToBelugaLoading = ref(null)
const retryToBelugaId = ref(null)

async function handleRetryToBelugaConfirmation(isConfirmed) {
  if (isConfirmed) {
    await retryToBeluga()
  }
}

async function retryToBeluga() {
  try {
    retryToBelugaLoading.value = true

    const { data } = await ApiService.get(`/admin/retry-order-send-to-whitelabelrx/${retryToBelugaId.value}`)

    if (data.status === 200) {
      await fetchItems()
      showSnackbar(data.message)
      retryToBelugaId.value = null
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    isRetryToBelugaDialogVisible.value = false
    retryToBelugaLoading.value = false
  }
}
</script>

<template>
  <section>
    <VRow class="match-height">
      <VCol cols="12">
        <VCard title="Filter">
          <!-- 👉 Filters -->
          <VCardText>
            <VRow>
              <VCol
                cols="12"
                sm="4"
              >
                <AppSelect
                  v-model="filterIsSentToWhiteLblRx"
                  label="Is Sent To WhiteLabelRx"
                  :items="[{ title: 'Sent', value: 1 }, { title: 'Failed', value: 7 }]"
                  clearable
                  clear-icon="tabler-x"
                  placeholder="Select"
                />
              </VCol>
              <VCol
                cols="12"
                sm="4"
              >
                <AppSelect
                  v-model="filterVisitType"
                  label="Visit Type"
                  :items="visitTypes"
                  clearable
                  multiple
                  clear-icon="tabler-x"
                  placeholder="Select"
                />
              </VCol>
              <VCol
                cols="12"
                sm="4"
              >
                <AppDateTimePicker
                  v-model="dateRange"
                  label="Created Date"
                  placeholder="Select Date Range"
                  variant="outlined"
                  :config="{
                    mode: 'range',
                    dateFormat: 'm/d/Y',
                    onClose: function (selectedDates, dateStr, instance) {
                      if (selectedDates.length === 1) {
                        instance.setDate([selectedDates[0], selectedDates[0]], true)
                      }
                    },
                    maxDate: new Date(),
                    minDate: (new Date()).setFullYear((new Date()).getFullYear() - 120),
                  }"
                  clearable
                  clear-icon="tabler-x"
                />
              </VCol>
            </VRow>
          </VCardText>

          <VDivider />

          <VCardText class="d-flex flex-wrap py-4 gap-4">
            <div
              class="me-3"
              style="width: 80px"
            >
              <VSelect
                v-model="rowPerPage"
                density="compact"
                variant="outlined"
                :items="[10, 20, 30, 50]"
              />
            </div>

            <VSpacer />

            <div class="d-flex align-center flex-wrap gap-4">
              <!-- 👉 Search  -->
              <div style="width: 23rem">
                <VTextField
                  v-model="searchQuery"
                  placeholder="Search"
                  density="compact"
                  clearable
                  clear-icon="tabler-x"
                />
              </div>
            </div>
          </VCardText>

          <VDivider />

          <VTable class="text-no-wrap">
            <!-- 👉 table head -->
            <thead>
              <tr>
                <th scope="col">
                  #
                </th>
                <th scope="col">
                  MASTER ID
                </th>
                <th scope="col">
                  SUBSCRIPTION / ORDER ID
                </th>
                <th scope="col">
                  USER
                </th>
                <th scope="col">
                  PRODUCT
                </th>
                <th scope="col">
                  VISIT TYPE
                </th>
                <th scope="col">
                  STATUS
                </th>
                <!--
                  <th scope="col">
                  BELUGA EVENT
                  </th>
                -->
                <th
                  scope="col"
                  class="cursor-pointer"
                  @click="sortColumn('created_at')"
                >
                  <div class="w-125px">
                    CREATED AT
                    <span class="d-grid float-right">
                      <VIcon
                        size="10"
                        icon="tabler-chevron-up"
                      />
                      <VIcon
                        size="10"
                        icon="tabler-chevron-down"
                      />
                    </span>
                  </div>
                </th>
                <th
                  scope="col"
                  class="cursor-pointer"
                  @click="sortColumn('updated_at')"
                >
                  <div class="w-125px">
                    UPDATED AT
                    <span class="d-grid float-right">
                      <VIcon
                        size="10"
                        icon="tabler-chevron-up"
                      />
                      <VIcon
                        size="10"
                        icon="tabler-chevron-down"
                      />
                    </span>
                  </div>
                </th>
                <th scope="col">
                  ACTIONS
                </th>
              </tr>
            </thead>

            <!-- 👉 table body -->
            <tbody v-if="skeletonLoading">
              <tr
                v-for="n in 10"
                :key="n"
              >
                <td
                  v-for="sk in 12"
                  :key="sk"
                >
                  <Skeleton
                    width="8rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
              </tr>
            </tbody>
            <tbody v-else>
              <tr
                v-for="item in items"
                :key="item.id"
                style="height: 3.75rem"
              >
                <!-- 👉 NO -->
                <td>
                  {{ item.sno }}
                </td>


                <!-- 👉 MASTER ID -->
                <td>
                  <RouterLink
                    :to="{
                      name: 'admin-visit-log-details',
                      params: { logId: item.id },
                      query: { ...route.query },
                    }"
                    class="dt-link"
                  >
                    {{ item.master_id }}
                  </RouterLink>
                </td>

                <!-- 👉 Subscription ID -->
                <td>
                  <RouterLink
                    :to="resolveRedirectLink(item)"
                    class="dt-link"
                  >
                    {{ item.subscription_reference_id }}
                  </RouterLink>
                </td>

                <!-- 👉 User -->
                <td class="title py-1">
                  <div>
                    <h6 class="text-base">
                      {{ item.user_detail?.first_name + " " + item.user_detail?.last_name }}
                    </h6>
                    <div class="d-flex flex-column">
                      <span class="text-body-2">{{ item.user_detail?.email }}</span>
                      <span class="text-body-2">{{ formattedPhoneNumber(item.user_detail?.phone_number) }}</span>
                    </div>
                  </div>
                </td>

                <!-- 👉 Product -->
                <td class="title">
                  <div class="d-flex align-center py-1">
                    <VAvatar
                      v-if="item.product_img"
                      variant="tonal"
                      size="40"
                      class="me-3"
                      :image="item.product_img"
                      rounded
                    />
                    <VAvatar
                      v-else
                      variant="tonal"
                      size="40"
                      class="me-3"
                      rounded
                    >
                      {{ resolveInitials(item.product_name) }}
                    </VAvatar>
                    <div>
                      <h6 class="text-base">
                        {{ item.product_name }}
                      </h6>
                      <div
                        v-if="item.drug_name"
                        class="text-body-2"
                      >
                        {{ item.drug_name }} <span v-if="item.provider_qty">({{ item.provider_qty }})</span>
                      </div>
                      <div class="d-flex flex-column">
                        <span
                          v-if="item.visit_type === 'weightloss' || item.visit_type === 'weightlossfollowup'"
                          class="d-block text-body-2"
                        >
                          {{ item.drug_month }} ({{ item.strength }} {{ item.strength_unit }})
                        </span>
                        <span
                          v-else
                          class="d-block text-body-2"
                        >
                          <span class="d-block">
                            {{ item.strength }} {{ item.strength_unit }} x {{ item.qty * item.subscription_interval }} units
                          </span>
                          <span
                            v-if="item.is_medicine_pickup_at_local_pharmacy !== 1"
                            class="d-block"
                          >
                            ({{ item.subscription_interval * 30 }}-day supply)
                          </span>
                        </span>
                      </div>
                    </div>
                  </div>
                </td>

                <!-- 👉 VISIT TYPE -->
                <td>
                  {{ item.visit_type }}
                </td>

                <!-- 👉 Status -->
                <td>
                  <VChip
                    label
                    :color="resolveVisitStatus(item.status).color"
                    size="small"
                    class="text-capitalize"
                  >
                    {{ resolveVisitStatus(item.status).label }}
                  </VChip>
                  <div
                    v-if="item.status === 6"
                    class="text-sm"
                  >
                    Visit canceled by patient
                  </div>
                  <VBtn
                    v-if="item.status === 0 || item.status === 7"
                    class="d-block mt-1"
                    size="x-small"
                    @click="() => {
                      retryToBelugaId = item.id
                      isRetryToBelugaDialogVisible = true
                    }"
                  >
                    <span v-if="item.status === 0">
                      <VIcon
                        icon="tabler-send"
                        start
                      />Send
                    </span>
                    <span v-if="item.status === 7">
                      <VIcon
                        icon="tabler-reload"
                        start
                      />Retry
                    </span>
                  </VBtn>
                </td>

                <!-- 👉 Visit event -->
                <!--
                  <td>
                  <span
                  v-if="!isEmpty(item.beluga_events)"
                  class="d-flex gap-1"
                  >
                  <VChip
                  v-for="status in item.beluga_events"
                  :key="status.event_status"
                  :color="status.event_status_color"
                  size="small"
                  class="text-capitalize"
                  label
                  >
                  {{ status.event_status }}
                  </VChip>
                  </span>
                  <span v-else>-</span>
                  </td>
                -->

                <!-- 👉 Created at -->
                <td>
                  <div class="d-flex flex-column">
                    <span>{{ item.created_date }}</span>
                    <span>{{ item.created_time }}</span>
                  </div>
                </td>

                <!-- 👉 Updated at -->
                <td>
                  <div
                    v-if="item.updated_date !== item.created_date || item.updated_time !== item.created_time"
                    class="d-flex flex-column"
                  >
                    <span>{{ item.updated_date }}</span>
                    <span>{{ item.updated_time }}</span>
                  </div>
                  <div v-else>
                    -
                  </div>
                </td>

                <!-- 👉 Actions -->
                <td
                  class="text-center"
                  style="width: 5rem"
                >
                  <VBtn
                    v-tooltip.top="'View Details'"
                    icon
                    size="x-small"
                    color="default"
                    variant="text"
                    :to="{
                      name: 'admin-visit-log-details',
                      params: { logId: item.id },
                      query: { ...route.query },
                    }"
                  >
                    <VIcon
                      size="22"
                      icon="tabler-eye"
                    />
                  </VBtn>
                </td>
              </tr>
            </tbody>

            <!-- 👉 table footer  -->
            <tfoot v-show="!skeletonLoading && items.length === 0">
              <tr>
                <td
                  colspan="10"
                  class="text-center"
                >
                  No data available
                </td>
              </tr>
            </tfoot>
          </VTable>

          <VDivider />

          <VCardText class="d-flex align-center flex-wrap justify-space-between gap-4 py-3 px-5">
            <span class="text-sm text-disabled">
              {{ paginationData }}
            </span>
            <VPagination
              v-model="currentPage"
              size="small"
              :total-visible="5"
              :length="totalPage"
            />
          </VCardText>
        </VCard>
      </VCol>
    </VRow>

    <!-- 👉 Confirm Retry Send to Provider -->
    <ConfirmDialog
      v-model:isDialogVisible="isRetryToBelugaDialogVisible"
      confirmation-question="Are you sure you want to retry sending this order to Provider?"
      :loading="retryToBelugaLoading"
      @confirm="handleRetryToBelugaConfirmation"
    />
  </section>
</template>

<style lang="scss">
.v-table__wrapper td.title {
  min-width: 200px;
  h6 {
    a {
      white-space: pre-line;
    }
  }
}
</style>
