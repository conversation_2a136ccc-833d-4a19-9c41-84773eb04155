<script setup>
import { usePreventNavigation } from '@/composables/usePreventNavigation'
import router from '@/router'
import ApiService from '@/services/ApiService'
import { formatCurrency } from '@/utils/helpers'
import { clearOrderSession } from '@/utils/sessionHelpers'
import Header from '@/views/user/components/Header.vue'
import { IconArrowRight, IconCheck, IconCircleFilled, IconExternalLink, IconRosetteDiscountCheckFilled } from '@tabler/icons-vue'
import { onMounted, ref } from 'vue'
import ConfettiExplosion from 'vue-confetti-explosion'
import { useRoute } from 'vue-router'

usePreventNavigation(true)

const route = useRoute()
const orderId = ref(route.params.orderId)
const skeletonLoading = ref(false)
const orderSummary = ref({})
const isLoading = ref(false)

onMounted(async () => {
  getOrderDetails()
  clearOrderSession()
})

async function getOrderDetails() {
  try {
    skeletonLoading.value = true

    const { data } = await ApiService.get(`/order-success/${orderId.value}`)
    if (data.status === 200) {
      orderSummary.value = data.orderSummary
    } else {
      console.error(data)
      router.push({ name: 'user-subscription' })
    }
  } catch (error) {
    console.error(error)
    router.push({ name: 'user-subscription' })
  } finally {
    skeletonLoading.value = false
  }
}
</script>

<template>
  <div>
    <Header />
    <div class="flex items-center justify-center py-6 sm:px-16">
      <div
        v-if="skeletonLoading"
        class="px-4 w-full sm:max-w-[600px]"
      >
        <div class="h-7 bg-gray-300 rounded mb-3 animate-pulse"></div>
        <div class="h-7 bg-gray-300 rounded mb-2 w-75 mx-auto animate-pulse"></div>
        <div class="h-12 bg-gray-300 rounded mt-6 mb-2 animate-pulse"></div>
        <div class="h-12 bg-gray-300 rounded mt-6 mb-2 animate-pulse"></div>
        <div class="h-12 bg-gray-300 rounded mt-6 mb-2 animate-pulse"></div>
        <div class="h-12 bg-gray-300 rounded mt-6 mb-2 animate-pulse"></div>
        <div class="h-12 bg-gray-300 rounded-xl mt-8 mb-2 animate-pulse"></div>
      </div>
      <div
        v-else
        class="px-4 w-full sm:max-w-[600px]"
      >
        <div class="flex flex-col mt-6 w-full">
          <div class="flex flex-col items-center justify-center">
            <ConfettiExplosion />
            <IconRosetteDiscountCheckFilled class="h-28 w-28 text-[#5c7844]" />
            <div class="text-2xl sm:text-3xl text-center font-semibold mt-4">
              {{
                route.query.verification === 'complete'
                  ? 'Thank you for completing your verification.'
                  : 'Thank you for your purchase.'
              }}
            </div>
          </div>
        </div>

        <div class="space-y-5 mt-10">
          <ol class="ps-2">
            <li class="relative border-s-4 !border-[#5c7844] pb-10">
              <div class="ms-8 bg-gray-100 rounded-3xl rounded-tl-none p-4 border-e-4 border-[#5c7844]">
                <div class="absolute w-8 h-8 bg-[#5c7844] rounded-full -start-[18px] grid place-items-center -mt-4">
                  <IconCheck
                    class="h-6 w-6 text-white"
                    stroke-width="2"
                  />
                </div>
                <div class="text-sm leading-none font-medium text-[#5c7844] mb-3">
                  Complete Visit
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-3">
                  The prescription has been submitted for provider evaluation.
                </h3>
                <div class="flex flex-col-reverse sm:flex-row align-start justify-between">
                  <div>
                    <h6 class="text-base font-medium text-gray-900 mb-2">
                      {{ orderSummary.product_name }}
                      <span v-if="orderSummary.frequency">
                        ({{ orderSummary.frequency?.charAt(0).toUpperCase() + orderSummary.frequency?.slice(1).replace('_', ' ') }})
                      </span>
                    </h6>
                    <div class="flex gap-2 text-sm text-gray-800 mb-2">
                      <span class="px-3 py-1 bg-black text-white rounded-lg font-medium text-xs">
                        {{ orderSummary.strength }} <span v-if="orderSummary.category_name === 'WL'">/ weekly</span>
                      </span>
                      <span
                        v-if="orderSummary.qty"
                        class="px-3 py-1 bg-black text-white rounded-lg font-medium text-xs"
                      >
                        {{ orderSummary.qty }} units<span v-if="orderSummary.is_medicine_pickup_at_local_pharmacy !== 1">/mo</span>
                      </span>
                    </div>
                    <!--
                      <div
                      v-if="orderSummary.is_medicine_pickup_at_local_pharmacy !== 1 && orderSummary.category_name !== 'WL'"
                      class="space-y-2"
                      >
                      <p class="text-sm text-gray-800 italic">
                      {{ orderSummary.plan_interval }} month plan billed {{ formatCurrency(orderSummary.plan_price) }} every {{ orderSummary.plan_interval }} {{ orderSummary.plan_interval === 1 ? 'month' : 'months' }}
                      </p>
                      </div>
                    -->
                  </div>
                  <div class="flex items-center justify-center">
                    <img
                      :src="orderSummary.image"
                      class="min-h-24 min-w-24 h-24 w-24 object-contain rounded-lg"
                      alt=""
                    >
                  </div>
                </div>
              </div>
            </li>
            <li class="relative border-s-4 border-dotted !border-[#E7900B] pb-10">
              <div class="ms-8 bg-gray-100 rounded-3xl rounded-tl-none p-4 border-e-4 border-[#E7900B]">
                <div class="absolute w-8 h-8 bg-[#E7900B] rounded-full -start-[18px] grid place-items-center -mt-4">
                  <IconCircleFilled
                    class="h-4 w-4 text-white"
                    stroke-width="2"
                  />
                </div>
                <div class="text-sm leading-none font-medium text-[#E7900B] mb-3">
                  Provider Review
                </div>
                <h3 class="text-3xl font-semibold text-gray-900 mb-4">
                  Your provider will get in touch with you soon about your prescription
                </h3>
                <div class="space-y-2">
                  <ul class="list-disc ps-5">
                    <li class="text-sm text-gray-800">
                      Review your medical history and condition.
                    </li>
                    <li class="text-sm text-gray-800">
                      Determine if this prescription is a good fit for you.
                    </li>
                    <li class="text-sm text-gray-800">
                      Leave you a message regarding your treatment.
                    </li>
                  </ul>
                </div>
              </div>
            </li>
            <li
              v-if="orderSummary.is_medicine_pickup_at_local_pharmacy === 1"
              class="relative mb-10"
            >
              <div class="ms-8 bg-gray-100 rounded-3xl rounded-tl-none p-4 border-e-4 border-[#E7900B]">
                <div class="absolute w-8 h-8 bg-[#E7900B] rounded-full -start-[14px] grid place-items-center -mt-4">
                </div>
                <div class="text-lg font-medium text-gray-800 mb-3">
                  Prescription Pickup Location
                </div>
                <h3 class="text-base text-gray-900 mb-2">
                  If approved, you will pick up your prescription at the following pharmacy.
                </h3>
                <div>
                  <address class="grid text-gray-700 mt-1.5">
                    <span class="font-medium">{{ orderSummary.pharmacy_name }}</span>
                    <span>{{ orderSummary.pharmacy_address }}</span>
                    <span>{{ orderSummary.pharmacy_city }}</span>
                    <span>{{ orderSummary.pharmacy_state }}-{{ orderSummary.pharmacy_zipcode }}</span>
                  </address>
                  <a
                    v-if="orderSummary && orderSummary.pharmacy_direction"
                    :href="orderSummary.pharmacy_direction"
                    class="text-black font-medium inline-flex items-center border-b !border-black mt-2"
                    target="_blank"
                    rel="noreferrer noopener"
                  >
                    <span>Get Directions</span>
                    <IconExternalLink
                      class="h-4 w-4 ms-1"
                      stroke-width="2"
                    />
                  </a>
                </div>
              </div>
            </li>
            <li
              v-else
              class="relative mb-10"
            >
              <div class="ms-8 bg-gray-100 rounded-3xl rounded-tl-none p-4 border-e-4 border-[#E7900B]">
                <div class="absolute w-8 h-8 bg-[#E7900B] rounded-full -start-[14px] grid place-items-center -mt-4">
                </div>
                <div class="text-lg font-medium text-gray-800 mb-3">
                  Treatment Shipped
                </div>
                <h3 class="text-base text-gray-900 mb-2">
                  If approved, we will charge the card you provided, <span class="font-medium">{{ formatCurrency(orderSummary.total_amount) }}</span>, and discreetly ship your prescription to your doorstep.
                </h3>
              </div>
            </li>
          </ol>
        </div>

        <TwButton
          class="mt-6 w-full mb-6"
          :loading="isLoading"
          @click="() => {
            isLoading = true
            if (orderSummary.is_medicine_pickup_at_local_pharmacy === 1) {
              // redirect to order details page
              router.push({
                name: 'user-order-details',
                params: { orderId: orderSummary.subscription_refill_id },
              })
            } else {
              // redirect to subscription details page
              router.push({
                name: 'user-manage-subscription',
                params: { subscriptionId: orderSummary.subscription_id },
              })
            }
          }"
        >
          {{
            orderSummary.is_medicine_pickup_at_local_pharmacy === 1
              ? 'Go to Order'
              : 'Go to Subscription'
          }}
          <IconArrowRight
            class="h-5 w-5 ms-2"
            stroke-width="2"
          />
        </TwButton>
      </div>
    </div>
  </div>
</template>
