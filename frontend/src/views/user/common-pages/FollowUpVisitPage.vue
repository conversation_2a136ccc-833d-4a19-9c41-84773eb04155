<script setup>
import Header from '../components/Header.vue'
import { IconArrowBack } from '@tabler/icons-vue'
import { computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'

// path: '/followup-visit/:category/:subscriptionId/overview'
const route = useRoute()
const followUpStarted = ref(false)
const currentStep = ref(1)

const categoryName = computed(() => {
  if (route.params.category === 'ed') {
    return 'Erecet Dysfunction'
  } else if (route.params.category === 'hl') {
    return 'Hair Loss'
  } else if (route.params.category === 'wl') {
    return 'Weight Loss'
  } else {
    return 'Unknown'
  }
})

const startFollowUp = () => {
  followUpStarted.value = true
  currentStep.value = 1
}

const goToSubscriptions = () => {
  alert('🔙 Returning to Subscriptions page...')
}

onMounted(async () => {
  //
})
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <Header show-back-button />

    <div class="container mx-auto px-6 py-8 max-w-4xl">
      <!-- Header -->
      <div class="mb-8">
        <div class="flex items-center mb-4">
          <button
            class="text-blue-600 hover:text-blue-800 flex items-center"
            @click="goToSubscriptions"
          >
            <IconArrowBack
              class="w-4 h-4 me-2"
              stroke-width="2"
            />
            Back to Subscriptions
          </button>
        </div>

        <h1 class="text-3xl font-bold text-gray-900 mb-2">
          Follow-Up Visit Management
        </h1>
        <p class="text-gray-600">
          Manage your follow-up appointments for {{ String(categoryName).toUpperCase() }} treatment
        </p>
      </div>

      <!-- Prescription Status Cards -->
      <div class="grid gap-6 md:grid-cols-2 mb-8">
        <!-- Valid Prescription Card -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center mb-4">
            <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
              <svg
                class="w-6 h-6 text-green-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                ></path>
              </svg>
            </div>
            <div class="ms-4">
              <h3 class="text-lg font-semibold text-green-800">
                Valid Prescription Found
              </h3>
              <p class="text-sm text-green-600">
                You're eligible for a follow-up visit
              </p>
            </div>
          </div>

          <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
            <div class="text-sm text-green-700 space-y-1">
              <p><strong>Medication:</strong> Generic Cialis</p>
              <p><strong>Dosage:</strong> 5.5 mg x 30 units</p>
              <p><strong>Valid Until:</strong> June 15, 2025</p>
            </div>
          </div>

          <button
            class="w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-4 rounded-md transition-colors flex items-center justify-center"
            @click="startFollowUp"
          >
            <svg
              class="w-5 h-5 me-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0V6a2 2 0 012-2h4a2 2 0 012 2v1m-6 0h8m-8 0H6a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V9a2 2 0 00-2-2h-2"
              ></path>
            </svg>
            Schedule Follow-Up Visit
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
