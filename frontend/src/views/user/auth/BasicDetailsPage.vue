<script setup>
import { isEmpty, isEmptyObject, isNullOrUndefined } from '@/@core/utils'
import router from '@/router'
import ApiService from '@/services/ApiService'
import { processErrors } from '@/utils/errorHandler'
import { preventSpacesFromStart, removeKeyFromObject } from '@/utils/helpers'
import AlertError from '@/views/user/components/AlertError.vue'
import Header from '@/views/user/components/Header.vue'
import PhoneVerificationModal from '@/views/user/components/PhoneVerificationModal.vue'
import { IconAlertCircle, IconInfoCircle } from '@tabler/icons-vue'
import { useSessionStorage } from '@vueuse/core'
import { parse } from 'date-fns/parse'
import { initFlowbite } from 'flowbite'
import { vMaska } from 'maska'
import { ErrorMessage, Field, Form as VForm } from 'vee-validate'
import { computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { toast } from 'vue-sonner'
import * as yup from 'yup'

const route = useRoute()
const onboardingSession = useSessionStorage('onboardingSession', {})
const isLoading = ref(false)
const inputErrors = ref({})
const serverErrors = ref([])
const states = ref([])
const phoneVerificationModal = ref(null)
const dobInputFocused = ref(false)
const birthDate = ref('')
const selectedState = ref('')
const visitType = computed(() => route.params.visitType)

const isTreatmentAvailableInSelectedState = computed(() => {
  return true

  if (isEmpty(selectedState.value) || isEmpty(states.value)) {
    return true
  }

  const state = states.value.find(s => s.code === selectedState.value && s.treatments.includes(visitType.value.toUpperCase()))

  return !!state
})

const stateDisabledMessage = computed(() => {
  const visitTypeFullText = {
    ED: 'ED',
    HL: 'Hair loss',
    WL: 'Weight loss',
  }

  return `We currently do not offer ${visitTypeFullText[visitType.value.toUpperCase()]} treatments in your state.`
})

const statesList = computed(() => states.value.filter(item => item.type === 'state'))
const territoriesList = computed(() => states.value.filter(item => item.type === 'territory'))

const validationSchema = yup.object().shape({
  first_name: yup.string().required('First name is required'),
  last_name: yup.string().required('Last name is required'),
  phone_number: yup.string().required('Phone Number is required'),
  dob: yup
    .date()
    .transform(function (value, originalValue) {
      if (this.isType(value)) {
        return value
      }

      return parse(originalValue, 'MM/dd/yyyy', new Date())
    })
    .typeError('Please enter a valid dob')
    .required('Date of birth is required')
    .min(new Date(new Date().setFullYear(new Date().getFullYear() - 100)), 'Dob must be at most 100 years old')
    .max(new Date(new Date().setFullYear(new Date().getFullYear() - 18)), 'Age must be at least 18 years old'),
  sex: yup.string().required('Sex assigned at birth is required'),
  state: yup.string().required('State / Territory is required'),
  race: yup.string().required('Race is required'),
})

const raceOptions = [
  'White',
  'Hispanic or Latino',
  'Black or African American',
  'Native American or American Indian',
  'Asian/Pacific Islander',
  'Other',
  'I prefer not to answer',
]

onMounted(() => {
  if (isEmpty(onboardingSession.value['userId'])) {
    router.replace({ name: 'ed-start-consult' })
  } else {
    initFlowbite()
    fetchStates()
  }
})

const fetchStates = async () => {
  try {
    const { data } = await ApiService.get('/state-list')

    if (data.status === 200) {
      states.value = data.stateData ?? []
    } else {
      console.error(data)
    }
  } catch (error) {
    console.error(error)
  }
}

async function handleSubmit(values) {
  try {
    isLoading.value = true
    inputErrors.value = {}
    serverErrors.value = []

    const postData = {
      ...values,
      phone_number: values.phone_number.replace(/\D/g, ''),
      user_id: onboardingSession.value['userId'],
    }

    const { data } = await ApiService.post('/update-user-health-details', postData)

    if (data.status === 200) {
      onboardingSession.value['phoneNumber'] = values.phone_number
      toast.success(data.message)
      openPhoneVerificationModal()
    } else {
      if (data.errors) {
        inputErrors.value = data.errors
      }

      // serverErrors.value = processErrors(data)
    }
  } catch (error) {
    if (error.response.errors) {
      inputErrors.value = error.response.errors
    }
    serverErrors.value = processErrors(error)
    console.error(error)
  } finally {
    isLoading.value = false
  }
}

function removeKeyFromInputErrors(key) {
  inputErrors.value = removeKeyFromObject(inputErrors.value, key)
}

const openPhoneVerificationModal = () => {
  phoneVerificationModal.value.open()
}

watch(birthDate, newVal => {
  if (newVal.length === 2) {
    birthDate.value += '/'
  } else if(newVal.length === 5) {
    birthDate.value += '/'
  }
})
</script>

<template>
  <div class="mx-auto max-w-[1920px]">
    <Header />

    <div class="flex items-center justify-center px-4 py-10 sm:px-6 sm:py-16 lg:px-8 lg:py-24">
      <div class="mx-auto sm:max-w-md">
        <h2 class="text-center text-2xl font-bold leading-tight text-black">
          Find out if you qualify for treatment. Register&nbsp;now!
        </h2>
        <p class="mt-2 text-center text-gray-600">
          It's just like the intake forms at the doctor 😊
        </p>

        <AlertError
          v-if="isEmptyObject(inputErrors) && serverErrors.length > 0"
          title="Error!"
          :errors="serverErrors"
        />

        <VForm
          class="mt-8"
          autocomplete="off"
          :validation-schema="validationSchema"
          @submit="handleSubmit"
        >
          <div class="space-y-5">
            <div class="flex flex-col md:flex-row gap-5">
              <div class="relative flex-auto">
                <Field
                  id="first_name"
                  type="text"
                  name="first_name"
                  class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                  placeholder=" "
                  autocomplete="off"
                  autofocus
                  @keydown="preventSpacesFromStart($event)"
                  @keyup="removeKeyFromInputErrors('first_name')"
                />
                <label
                  for="first_name"
                  class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4 cursor-text"
                >First Name</label>
                <ErrorMessage
                  name="first_name"
                  class="text-red-500 text-sm ms-5"
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.first_name)"
                  class="text-red-500 text-sm ms-5"
                >
                  {{ inputErrors.first_name[0] }}
                </p>
              </div>

              <div class="relative flex-auto">
                <Field
                  id="last_name"
                  type="text"
                  name="last_name"
                  class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                  placeholder=" "
                  autocomplete="off"
                  @keydown="preventSpacesFromStart($event)"
                  @keyup="removeKeyFromInputErrors('last_name')"
                />
                <label
                  for="last_name"
                  class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4 cursor-text"
                >Last Name</label>
                <ErrorMessage
                  name="last_name"
                  class="text-red-500 text-sm ms-5"
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.last_name)"
                  class="text-red-500 text-sm ms-5"
                >
                  {{ inputErrors.last_name[0] }}
                </p>
              </div>
            </div>

            <div class="space-y-2">
              <label class="font-medium text-gray-500 duration-300 flex items-center">
                <span>Sex assigned at birth</span>
                <button
                  type="button"
                  class="ms-2 cursor-pointer"
                  data-tooltip-target="tooltip-gender"
                  data-tooltip-placement="right"
                  data-tooltip-trigger="click"
                >
                  <IconInfoCircle
                    class="w-4 h-4 mb-1"
                    stroke-width="2"
                  />
                </button>
                <div
                  id="tooltip-gender"
                  role="tooltip"
                  class="absolute z-10 max-w-[200px] invisible inline-block px-3 py-2 text-sm font-medium text-white bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip"
                >
                  Your sex assigned at birth is important for your doctor to make medical decisions.
                  <div
                    class="tooltip-arrow"
                    data-popper-arrow
                  ></div>
                </div>
              </label>

              <ul class="grid w-full gap-3 md:grid-cols-3">
                <li>
                  <Field
                    id="gender-male"
                    type="radio"
                    name="sex"
                    value="Male"
                    class="hidden peer"
                    required
                  />
                  <label
                    for="gender-male"
                    class="inline-flex items-center justify-center w-full p-3 text-gray-500 bg-white border-2 !border-gray-200 rounded-lg cursor-pointer peer-checked:!border-black peer-checked:text-black hover:text-gray-600 hover:bg-gray-100"
                  >
                    <div class="block">
                      <div class="w-full text-base font-medium">Male</div>
                    </div>
                  </label>
                </li>
                <li>
                  <Field
                    id="gender-female"
                    type="radio"
                    name="sex"
                    value="Female"
                    class="hidden peer"
                  />
                  <label
                    for="gender-female"
                    class="inline-flex items-center justify-center w-full p-3 text-gray-500 bg-white border-2 !border-gray-200 rounded-lg cursor-pointer peer-checked:!border-black peer-checked:text-black hover:text-gray-600 hover:bg-gray-100"
                  >
                    <div class="block">
                      <div class="w-full text-base font-medium">Female</div>
                    </div>
                  </label>
                </li>
                <li>
                  <Field
                    id="gender-other"
                    type="radio"
                    name="sex"
                    value="Other"
                    class="hidden peer"
                  />
                  <label
                    for="gender-other"
                    class="inline-flex items-center justify-center w-full p-3 text-gray-500 bg-white border-2 !border-gray-200 rounded-lg cursor-pointer peer-checked:!border-black peer-checked:text-black hover:text-gray-600 hover:bg-gray-100"
                  >
                    <div class="block">
                      <div class="w-full text-base font-medium">Other</div>
                    </div>
                  </label>
                </li>
              </ul>
              <ErrorMessage
                name="sex"
                class="text-red-500 text-sm ms-5"
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.sex)"
                class="text-red-500 text-sm ms-5"
              >
                {{ inputErrors.sex[0] }}
              </p>
            </div>

            <div>
              <div class="relative">
                <Field
                  id="dob"
                  v-model="birthDate"
                  v-maska
                  type="text"
                  name="dob"
                  class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                  :placeholder="dobInputFocused ? 'mm/dd/yyyy' : ' '"
                  autocomplete="off"
                  validate-on-input
                  data-maska="##/##/####"
                  @focus="dobInputFocused = true"
                  @blur="dobInputFocused = false"
                  @keyup="removeKeyFromInputErrors('dob')"
                />
                <div
                  class="cursor-pointer absolute inset-y-0 right-0 flex items-center px-2 text-gray-500"
                  data-tooltip-target="tooltip-dob"
                  data-tooltip-trigger="click"
                >
                  <IconInfoCircle
                    class="w-4 h-4"
                    stroke-width="2"
                  />
                </div>
                <div
                  id="tooltip-dob"
                  role="tooltip"
                  class="absolute z-10 max-w-[200px] invisible inline-block px-3 py-2 text-sm font-medium text-white bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip"
                >
                  Your birthdate is required to determine if you're eligible for telemedicine.
                  <div
                    class="tooltip-arrow"
                    data-popper-arrow
                  ></div>
                </div>
                <label
                  for="dob"
                  class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4 cursor-text"
                >Birth date</label>
              </div>
              <ErrorMessage
                name="dob"
                class="text-red-500 text-sm ms-5"
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.dob)"
                class="text-red-500 text-sm ms-5"
              >
                {{ inputErrors.dob[0] }}
              </p>
            </div>

            <div class="relative">
              <Field
                id="state"
                v-model="selectedState"
                as="select"
                name="state"
                class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                required
              >
                <option
                  value=""
                  disabled
                  selected
                >
                  Select your state
                </option>
                <optgroup label="States">
                  <option
                    v-for="state in statesList"
                    :key="state.code"
                    :value="state.code"
                  >
                    {{ state.name }}
                  </option>
                </optgroup>
                <optgroup label="Territories">
                  <option
                    v-for="territory in territoriesList"
                    :key="territory.code"
                    :value="territory.code"
                  >
                    {{ territory.name }}
                  </option>
                </optgroup>
              </Field>
              <label
                for="state"
                class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-focus:scale-75 peer-focus:-translate-y-4"
              >State / Territory</label>
              <ErrorMessage
                name="state"
                class="text-red-500 text-sm ms-5"
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.state)"
                class="text-red-500 text-sm ms-5"
              >
                {{ inputErrors.state[0] }}
              </p>

              <div
                v-if="!isTreatmentAvailableInSelectedState"
                class="text-red-500 text-sm flex justify-center mt-2"
              >
                <IconAlertCircle
                  class="size-4 me-2"
                  stroke-width="2"
                />
                <span>{{ stateDisabledMessage }}</span>
              </div>
            </div>

            <div>
              <div class="relative">
                <Field
                  id="phone_number"
                  v-maska
                  type="text"
                  name="phone_number"
                  class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                  placeholder=" "
                  autocomplete="off"
                  data-maska="(###) ###-####"
                  required
                  @keyup="removeKeyFromInputErrors('phone_number')"
                />
                <div
                  class="cursor-pointer absolute inset-y-0 right-0 flex items-center px-2 text-gray-500"
                  data-tooltip-target="tooltip-phone"
                  data-tooltip-trigger="click"
                >
                  <IconInfoCircle
                    class="w-4 h-4"
                    stroke-width="2"
                  />
                </div>
                <div
                  id="tooltip-phone"
                  role="tooltip"
                  class="absolute z-10 max-w-[200px] invisible inline-block px-3 py-2 text-sm font-medium text-white bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip"
                >
                  Your phone number is required to verify your account.
                  <div
                    class="tooltip-arrow"
                    data-popper-arrow
                  ></div>
                </div>
                <label
                  for="phone_number"
                  class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4 cursor-text"
                >Phone Number</label>
              </div>
              <ErrorMessage
                name="phone_number"
                class="text-red-500 text-sm ms-5"
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.phone_number)"
                class="text-red-500 text-sm ms-5"
              >
                {{ inputErrors.phone_number[0] }}
              </p>
            </div>

            <div class="relative">
              <Field
                id="race"
                as="select"
                name="race"
                class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                required
              >
                <option
                  value=""
                  disabled
                  selected
                >
                  Select your race
                </option>
                <option
                  v-for="item in raceOptions"
                  :key="item"
                  :value="item"
                >
                  {{ item }}
                </option>
              </Field>
              <label
                for="race"
                class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-focus:scale-75 peer-focus:-translate-y-4"
              >What's your race?</label>
              <ErrorMessage
                name="race"
                class="text-red-500 text-sm ms-5"
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.race)"
                class="text-red-500 text-sm ms-5"
              >
                {{ inputErrors.race[0] }}
              </p>
            </div>

            <div class="mt-8">
              <TwButton
                type="submit"
                class="w-full"
                :loading="isLoading"
                :disabled="!isTreatmentAvailableInSelectedState"
              >
                Next
              </TwButton>
            </div>
          </div>
        </VForm>
        <div class="mt-8 text-center">
          <p class="text-sm text-gray-600">
            For patient safety, provide your phone number for verification.
            Agree to receive text messages for verification and account purposes.
            Standard rates may apply.
          </p>
        </div>
      </div>
    </div>

    <PhoneVerificationModal
      ref="phoneVerificationModal"
      :close-on-click-outside="false"
    />
  </div>
</template>
