<script setup>
import { isEmpty } from '@/@core/utils'
import ApiService from '@/services/ApiService'
import { useAuthStore } from '@/store/auth'
import { processErrors } from '@/utils/errorHandler'
import { createIdFromString } from '@/utils/helpers'
import AlertError from '@/views/user/components/AlertError.vue'
import ModalHelp from '@/views/user/components/ModalHelp.vue'
import { IconCheck, IconChevronLeft, IconHelp, IconLoader2, IconLoader3, IconMoodSad, IconCloudUpload } from '@tabler/icons-vue'
import { useDebounceFn, useSessionStorage } from '@vueuse/core'
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import Accordion from '../components/Accordion.vue'
import { toast } from 'vue-sonner'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

const edVisitSession = useSessionStorage('edVisitSession', {})
const isLoading = ref(false)
const isFinalLoading = ref(false)
const isNextBtnDisabled = ref(false)
const radioOptionLoading = ref(null)
const serverErrors = ref([])
const questions = ref([])
const currentQuestionIndex = ref(0)
const currentSlug = ref(route.params.slug)
const modalHelpRef = ref(null)
const isNavigatingBack = ref(false)

const treatmentVisitType = computed(() => {
  if (route.name === 'ed-followup-questions') return 'followup'
  else if (route.name === 'ed-questions-renew') return 'renew'
  else return 'new'
})

const currentQuestion = computed(() => {
  if (currentQuestionIndex.value >= 0 && currentQuestionIndex.value < questions.value.length) {
    return questions.value[currentQuestionIndex.value]
  }

  return null
})

async function fetchQuestions() {
  try {
    isLoading.value = true

    const newVisitQuestions = '/ed-question-list'
    const followupQuestions = `/ed-followup-question-list/${edVisitSession.value['subscriptionId']}`

    const { data } = await ApiService.get(
      treatmentVisitType.value === 'followup' ? followupQuestions : newVisitQuestions,
    )

    if (data.status === 200) {
      questions.value = data.questionData

      if (sessionStorage.getItem('edQuestionsSession') && authStore.isAuthenticated) {
        const questionsSession = JSON.parse(sessionStorage.getItem('edQuestionsSession'))

        questions.value[0].answer_value = questionsSession.session[questions.value[0].slug]
        questions.value[1].answer_value = questionsSession.session[questions.value[1].slug]
        questions.value[2].answer_value = questionsSession.session[questions.value[2].slug]
        debouncedValidateQuestions()

        currentQuestionIndex.value = questionsSession.next_question_index
        await router.replace({ name: route.name, params: { slug: questionsSession.next_question_slug } })
        setTimeout(() => {
          isLoading.value = false
        }, 1500)
      } else {
        router.replace({ name: route.name, params: { slug: questions.value[0].slug } })
        isLoading.value = false
      }

      sessionStorage.removeItem('edQuestionsSession')
    } else if (data.status === 403) {
      // User not allowed to start new visit
      toast.error(data.message || 'You are not allowed to start a new visit.')
      router.push({ name: 'user-subscription' })
    } else {
      isLoading.value = false
      serverErrors.value = processErrors(data)
    }
  } catch (error) {
    isLoading.value = false
    console.error(error)
    serverErrors.value = processErrors(error)
  }
}

watch(route, () => {
  if (route.params.slug === 'start') {
    router.replace({ name: route.name, params: { slug: questions.value[0]?.slug || 'start' } })
  }
  currentSlug.value = Number(route.params.slug)
})

watch(currentSlug, async () => {
  if (currentSlug.value !== 'start') {
    let quesIndex = questions.value.findIndex(question => question.slug === currentSlug.value)

    if (quesIndex !== -1) {
      currentQuestionIndex.value = quesIndex
    }
  }
})

watch(currentQuestion, () => {
  if (currentQuestion.value && currentQuestion.value.slug) {
    router.push({ name: route.name, params: { slug: currentQuestion.value.slug } })
  }
})

const questionProgressCount = computed(() => {
  if (isLoading.value) return 0

  return questions.value.length > 0
    ? (100 / (questions.value.length + 1)) * (currentQuestionIndex.value + 1)
    : 0
})

const questionOptions = computed(() => {
  const currQuestionValue = currentQuestion.value

  if (!isEmpty(currQuestionValue) && ['radio', 'checkbox'].includes(currQuestionValue?.answer_type)) {
    const allOptions = currQuestionValue.options?.filter(o => (!o.hidden))

    // If question has filter option enabled
    if (currQuestionValue.meta.filter_options) {
      const parentQues = questions.value?.find(q => q.id === currQuestionValue.meta.parent_question_id)

      if (isEmpty(parentQues)) return []

      const parentQuesAnswer = parentQues.answer_type === 'checkbox'
        ? parentQues.answer_value
        : [parentQues.answer_value]

      const options = currQuestionValue.options.filter(option => {
        return parentQues.options.find(o => (
          option.parent_option_id.includes(o.id) && parentQuesAnswer.includes(o.value)
        ))
      })

      return options
    }

    // else return all options
    return allOptions
  }

  return []
})

const selectedParentQuesOptions = computed(() => {
  if (currentQuestion.value && currentQuestion.value.meta?.show_options) {
    const parentQues = questions.value.find(q => q.id === currentQuestion.value.meta?.parent_question_id)
    if (parentQues) {
      return {
        show: true,
        selectedOptions: Array.isArray(parentQues.answer_value) ? parentQues.answer_value : [parentQues.answer_value],
      }
    }
  }

  return {
    show: false,
    selectedOptions: [],
  }
})

const debouncedValidateQuestions = useDebounceFn(validate, 300)

async function validate() {
  try {
    isNextBtnDisabled.value = true
    serverErrors.value = []

    if (isAnswerDisqualified.value) return

    const cleanedQuesAns = cleanQuestionAnswers()
    const slug = cleanedQuesAns[cleanedQuesAns.length - 1].slug

    const postData = {
      category: 'ED',
      current_question_slug: slug,
      question_answers: cleanQuestionAnswers(),
    }

    if (edVisitSession.value['questionSessionId']) {
      postData.question_session_id = edVisitSession.value['questionSessionId']
    }
    if (treatmentVisitType.value === 'followup') {
      postData.followup_visit_id = edVisitSession.value['subscriptionId']
    }
    if (treatmentVisitType.value === 'renew') {
      postData.renewed_subscription_id = edVisitSession.value['subscriptionId']
    }

    const { data } = await ApiService.post(
      treatmentVisitType.value === 'followup'
        ? '/validating-ed-followup-question-answers'
        : '/validating-ed-question-answers',
      postData,
    )

    if (data.status === 200) {
      if (data.is_authentication_required && data.is_authentication_required === 1) {
        router.push({ name: 'user-login', query: { redirect_to: route.fullPath } })
      } else {
        edVisitSession.value['questionSessionId'] = data.question_session_id

        if (data.is_completed_question_answers === 1) {
          isFinalLoading.value = true
          setTimeout(() => {
            if (treatmentVisitType.value === 'followup') {
              edVisitSession.value['visitType'] = 'followup'
              router.push({ name: 'ed-visit-current-treatment', query: { ...route.query } })
            } else if (treatmentVisitType.value === 'renew') {
              edVisitSession.value['visitType'] = 'renew'
              router.push({ name: 'ed-visit-current-treatment', query: { ...route.query } })
            } else {
              edVisitSession.value['visitType'] = 'new'
              router.push({ name: 'ed-questions-complete', query: { ...route.query } })
            }
          }, 2000)
        }
      }
      nextQuestion()
    } else if (data.status === 403) {
      // User not allowed to start new visit
      toast.error(data.message || 'You are not allowed to start a new visit.')
      router.push({ name: 'user-subscription' })
    } else {
      serverErrors.value = processErrors(data)
    }
  } catch (error) {
    console.error(error)
    serverErrors.value = processErrors(error)
  } finally {
    isNextBtnDisabled.value = false
    radioOptionLoading.value = null
  }
}

function cleanQuestionAnswers() {
  const cleanedArray = []
  const questionsValue = JSON.parse(JSON.stringify(questions.value))

  for (let i = 0; i <= currentQuestionIndex.value; i++) {
    const question = questionsValue[i]

    if (!question.meta.is_sub_question) {
      cleanedArray.push(question)
      continue
    }

    const parentQues = questionsValue.find(q => q.id === question.meta.parent_question_id)
    if (!parentQues) continue

    const optionValue = parentQues.options.find(o => o.id === question.meta.parent_option_id)?.value
    const isRadioAndSelected = parentQues.answer_type === 'radio' && optionValue === parentQues.answer_value
    const isCheckboxAndSelected = parentQues.answer_type === 'checkbox' && parentQues.answer_value.includes(optionValue)

    if (isRadioAndSelected || isCheckboxAndSelected) {
      cleanedArray.push(question)
    } else {
      question.answer_value = question.answer_type === 'checkbox' ? [] : ''
    }
  }

  return cleanedArray
}

function nextQuestion() {
  // Handle authentication requirement
  if (currentQuestionIndex.value === 2 && !authStore.isAuthenticated) {
    const questionsSessionData = {
      [questions.value[0].slug]: questions.value[0].answer_value,
      [questions.value[1].slug]: questions.value[1].answer_value,
      [questions.value[2].slug]: questions.value[2].answer_value,
    }

    sessionStorage.setItem('edQuestionsSession', JSON.stringify({
      session: questionsSessionData,
      next_question_index: 2, // because we are validating first then updating the index
      next_question_slug: questions.value[2].slug,
    }))

    router.push({ name: 'user-signup', params: { visitType: 'ed' } })

    return
  }

  if (currentQuestionIndex.value >= questions.value.length || isAnswerDisqualified.value) {
    return
  }

  const nextQues = questions.value[currentQuestionIndex.value + 1]
  if (nextQues && nextQues.meta.is_sub_question) {
    const parentQues = questions.value.find(question => question.id === nextQues.meta.parent_question_id)
    if (parentQues) {
      const optionValue = parentQues.options.find(o => o.id === nextQues.meta.parent_option_id)?.value
      if (
        (parentQues.answer_type === 'radio' && optionValue === parentQues.answer_value) ||
        (parentQues.answer_type === 'checkbox' && parentQues.answer_value.includes(optionValue))
      ) {
        currentQuestionIndex.value++
      } else {
        currentQuestionIndex.value++
        nextQuestion()
      }
    }
  } else {
    currentQuestionIndex.value++
  }
}

const isAnswerDisqualified = computed(() => {
  let disqualified = false

  if (currentQuestion.value?.answer_type === 'radio' || currentQuestion.value?.answer_type === 'checkbox') {
    disqualified = questionOptions.value?.some(option => {
      const userAnswer = currentQuestion.value?.answer_type === 'checkbox'
        ? currentQuestion.value?.answer_value
        : [currentQuestion.value?.answer_value]

      return userAnswer.includes(option.value) && option.is_disqualified === 1
    })
  }

  return disqualified
})

const showNextButton = computed(() => {
  if (isAnswerDisqualified.value) {
    return false
  } else if (['text', 'textarea', 'textbox', 'checkbox', 'file'].includes(currentQuestion.value?.answer_type)) {
    return !isEmpty(currentQuestion.value?.answer_value)
  } else if (currentQuestion.value?.answer_type === 'radio') {
    return false
  } else {
    return false
  }
})

const handleCheckboxUpdate = option => {
  if (option.select_one && option.select_one === 1) {
    currentQuestion.value.answer_value = [option.value]
  } else {
    const noneOption = currentQuestion.value.options.find(option => option.select_one && option.select_one === 1)

    if (noneOption) {
      const index = currentQuestion.value.answer_value.indexOf(noneOption.value)
      if (index > -1) {
        currentQuestion.value.answer_value.splice(index, 1)
      }
    }
  }
}

function handleBackClick(isBrowserBack = false) {
  function updateAnswer() {
    if (currentQuestion.value?.answer_type === 'checkbox') {
      currentQuestion.value.answer_value = []
    } else {
      currentQuestion.value.answer_value = ''
    }
  }

  function navigateBack() {
    isNavigatingBack.value = true
    setTimeout(() => {
      isNavigatingBack.value = false
    }, 500)

    if (!isBrowserBack) router.go(-1)
  }

  if (isAnswerDisqualified.value) {
    updateAnswer()
  } else if (currentQuestion.value?.meta?.is_common_question) {
    navigateBack()
  } else {
    updateAnswer()
    navigateBack()
  }
}

// const selectedFile = ref({ file: null, url: null })
const fileDropZoneRef = ref()
const fileInputRef = ref()

function onFileDrop(files) {
  handleSelectedFiles(files)
}

function handleFileSelection() {
  fileInputRef.value.click()
}

function onChangeFile() {
  handleSelectedFiles(fileInputRef.value.files)
}

async function handleSelectedFiles(files) {
  if (files.length > 0) {
    const file = files[0]

    if (file.size > 3 * 1024 * 1024) {
      toast.error('File size should be less than 3MB.')

      return
    }

    const allowedFileTypes = String(currentQuestion.value.allowed_file_types).split(',')

    if (!allowedFileTypes.includes(file.type)) {
      toast.error(currentQuestion.value.file_error)

      return
    }

    const url = await readAsDataURL(file)

    // selectedFile.value = { file, url }
    currentQuestion.value.answer_value = url
  } else {
    // selectedFile.value = { file: null, url: null }
    toast.error('Please select an image.')
  }
}

useDropZone(fileDropZoneRef, onFileDrop)

function reupload() {
  // selectedFile.value = { file: null, url: null }
  currentQuestion.value.answer_value = ''
}

function onLoadListener(e) {
  e.preventDefault()

  const message = 'You have unsaved changes. Are you sure you wish to leave?'

  e.returnValue = message

  return message
}

onMounted(async () => {
  if (route.params.slug === 'start' && treatmentVisitType.value === 'new') {
    edVisitSession.value = {}
    edVisitSession.value['questionSessionId'] = null
  }
  await fetchQuestions()

  window.addEventListener('beforeunload', onLoadListener)

  // listen for back button press
  window.addEventListener('popstate', () => {
    handleBackClick(true)
  })
})

onUnmounted(() => {
  window.removeEventListener('beforeunload', onLoadListener)
  window.removeEventListener('popstate', () => {})
})
</script>

<template>
  <div>
    <nav class="flex justify-space-between items-center px-11 py-6 w-full bg-white max-md:px-5 max-md:max-w-full">
      <div
        class="cursor-pointer"
        :class="[currentQuestionIndex > 0 ? 'visible' : 'invisible']"
        @click="handleBackClick(false)"
      >
        <IconChevronLeft
          stroke-width="2.5"
          class="w-6 h-6"
        />
      </div>
      <AppLogo />
      <div>
        <button
          v-tooltip.bottom="'Help'"
          type="button"
          class="absolute top-[10px] right-[10px] cursor-pointer p-4 disabled:cursor-not-allowed disabled:text-gray-500"
          @click="modalHelpRef.open()"
        >
          <IconHelp
            class="h-6 w-6 cursor-pointer text-black"
            stroke-width="2"
          />
        </button>
      </div>
    </nav>

    <div class="mx-auto max-w-screen-xl">
      <div class="mx-auto max-w-2xl px-4 sm:px-6 lg:px-8 text-center pb-[100px]">
        <div class="mb-10 mt-6 max-w-lg mx-auto">
          <div
            class="w-full bg-gray-200 rounded-full h-1.5"
            data-tooltip-target="tooltip-progress-percentage"
            data-tooltip-trigger="hover"
          >
            <div
              class="bg-[#ffef08] h-1.5 rounded-full"
              :style="`width: ${questionProgressCount}%`"
            ></div>
          </div>
        </div>

        <AlertError
          v-if="serverErrors.length > 0"
          title="Error!"
          :errors="serverErrors"
        />

        <!-- 👉 Loading state -->
        <div
          v-if="isLoading"
          role="status"
          class="animate-pulse"
        >
          <div class="flex flex-col justify-center items-center space-y-3 mb-10">
            <div class="h-6 bg-gray-200 rounded-md w-[80%]"></div>
            <div class="h-6 bg-gray-200 rounded-md w-full"></div>
            <div class="h-6 bg-gray-200 rounded-md w-[65%]"></div>
          </div>

          <div class="space-y-5 mx-auto max-w-lg">
            <div class="h-10 bg-gray-200 rounded-full"></div>
            <div class="h-10 bg-gray-200 rounded-full"></div>
            <div class="h-10 bg-gray-200 rounded-full"></div>
            <div class="h-10 bg-gray-200 rounded-full"></div>
          </div>
        </div>

        <div v-if="isFinalLoading">
          <div class="text-center py-10">
            <IconLoader3 class="inline w-14 h-14 text-gray-800 animate-spin" />
            <p class="text-2xl font-medium mt-6">
              Analyzing your responses...
            </p>
          </div>
        </div>

        <!-- 👉 Normal state -->
        <transition
          v-if="!isLoading && !isFinalLoading"
          :name="isNavigatingBack ? 'slide-right' : 'slide-left'"
          mode="out-in"
        >
          <div :key="currentQuestion?.question">
            <div>
              <!-- Is Factoid  -->
              <div
                v-if="currentQuestion?.is_factoid"
                class="max-w-lg mx-auto"
              >
                <div v-html="currentQuestion?.factoid"></div>
              </div>

              <!-- Is Question -->
              <div v-else>
                <div v-if="!isAnswerDisqualified">
                  <div>
                    <h5
                      v-if="isEmpty(currentQuestion?.agreement)"
                      class="text-xl md:text-3xl text-black font-medium"
                    >
                      {{ currentQuestion?.question }}
                    </h5>
                    <p
                      v-if="!isEmpty(currentQuestion?.agreement)"
                      class="text-black font-medium text-lg mt-3"
                    >
                      {{ currentQuestion?.sub_text }}
                    </p>
                    <p
                      v-else-if="!isEmpty(currentQuestion?.sub_text)"
                      class="text-gray-600 text-sm mt-3"
                    >
                      ({{ currentQuestion?.sub_text }})
                    </p>

                    <!-- Selected options from parent question -->
                    <p
                      v-if="selectedParentQuesOptions.show"
                      class="text-gray-600 text-sm mt-3"
                    >
                      (<strong>*Selected conditions:</strong> {{ selectedParentQuesOptions.selectedOptions.join(', ') }})
                    </p>

                    <div
                      v-if="!isEmpty(currentQuestion?.important_note)"
                      class="mt-6 bg-red-50 p-4 text-start rounded-2xl"
                    >
                      <span class="text-red-500 uppercase font-medium">Important:</span> {{ currentQuestion?.important_note }}
                    </div>
                  </div>

                  <!-- consent agreement content -->
                  <div
                    v-if="!isEmpty(currentQuestion?.agreement)"
                    class="mx-auto max-w-lg mt-10 mb-6 text-start"
                  >
                    <Accordion :title="currentQuestion?.question">
                      <div
                        class="text-start text-gray-700 prose-sm prose-h5:text-black prose-h5:font-semibold prose-ul:list-disc"
                        v-html="currentQuestion?.agreement"
                      ></div>
                    </Accordion>
                  </div>

                  <div class="mx-auto max-w-lg">
                    <!-- 👉 if question type is text -->
                    <div v-if="currentQuestion?.answer_type === 'text'">
                      <input
                        v-model="currentQuestion.answer_value"
                        class="w-full mt-8 border !border-gray-300 rounded-lg p-3 focus:ring-black"
                        placeholder="Write here..."
                      />
                    </div>

                    <!-- 👉 if question type is textarea -->
                    <div v-if="currentQuestion?.answer_type === 'textarea'">
                      <textarea
                        v-model="currentQuestion.answer_value"
                        class="w-full mt-8 border !border-gray-300 rounded-lg p-3 focus:ring-black"
                        placeholder="Write here..."
                        rows="4"
                      ></textarea>
                    </div>

                    <!-- 👉 if question type is radio -->
                    <ul
                      v-if="currentQuestion?.answer_type === 'radio'"
                      class="w-full mt-8 space-y-5"
                    >
                      <li
                        v-for="option in questionOptions"
                        :key="createIdFromString(option.value)"
                      >
                        <input
                          :id="createIdFromString(option.value)"
                          v-model="currentQuestion.answer_value"
                          :name="currentQuestion?.slug"
                          :value="option.value"
                          class="hidden peer"
                          type="radio"
                          :disabled="isNextBtnDisabled"
                        />
                        <label
                          :for="createIdFromString(option.value)"
                          class="inline-flex justify-between items-center w-full px-5 py-3 text-gray-700 bg-white border-2 border-gray-200 rounded-full peer-checked:!border-black peer-checked:text-black hover:bg-gray-100"
                          :class="{
                            'cursor-pointer': isEmpty(radioOptionLoading),
                            'cursor-not-allowed': !isEmpty(radioOptionLoading),
                          }"
                          @click="() => {
                            radioOptionLoading = option.id
                            if (!isNextBtnDisabled) {
                              debouncedValidateQuestions()
                            }
                          }"
                        >
                          <span :class="`block h-4 w-4 ${radioOptionLoading === option.id ? 'visible' : 'invisible'}`"></span>
                          <span class="block">
                            <span class="w-full text-base font-medium">{{ option.value }}</span>
                          </span>
                          <span :class="`block ${radioOptionLoading === option.id ? 'visible' : 'invisible'}`">
                            <IconLoader2
                              class="h-4 w-4 animate-spin"
                              stroke-width="2"
                            />
                          </span>
                        </label>
                      </li>
                    </ul>

                    <!-- 👉 if question type is checkbox -->
                    <ul
                      v-if="currentQuestion?.answer_type === 'checkbox'"
                      class="w-full mt-8 space-y-5"
                    >
                      <li
                        v-for="option in questionOptions"
                        :key="createIdFromString(option.value)"
                      >
                        <input
                          :id="createIdFromString(option.value)"
                          v-model="currentQuestion.answer_value"
                          :name="`${currentQuestion?.slug}[]`"
                          :value="option.value"
                          class="hidden peer"
                          type="checkbox"
                          @change="handleCheckboxUpdate(option)"
                        />
                        <label
                          v-if="!isEmpty(currentQuestion?.agreement)"
                          :for="createIdFromString(option.value)"
                          class="inline-flex items-center w-full text-gray-800"
                        >
                          <span class="me-3 flex items-center justify-center cursor-pointer">
                            <span
                              class="w-6 h-6 border-2 rounded-sm flex justify-center items-center"
                              :class="[
                                currentQuestion?.answer_value.includes(option.value) ? 'border-transparent bg-black' : 'border-gray-400'
                              ]"
                            >
                              <IconCheck
                                class="h-5 w-5 z-50"
                                :stroke-width="currentQuestion?.answer_value.includes(option.value) ? 4 : 1"
                                :class="[
                                  currentQuestion?.answer_value.includes(option.value) ? 'text-white' : 'text-gray-900'
                                ]"
                              />
                            </span>
                          </span>
                          <span class="block text-start cursor-pointer">
                            <span class="w-full text-base font-medium">{{ option.value }}</span>
                          </span>
                        </label>
                        <label
                          v-else
                          :for="createIdFromString(option.value)"
                          class="inline-flex items-center justify-between w-full px-5 py-3 text-gray-700 bg-white border-2 border-gray-200 rounded-full cursor-pointer peer-checked:!border-black peer-checked:text-black hover:bg-gray-100"
                        >
                          <span class="block text-start">
                            <span class="w-full text-base font-medium">{{ option.value }}</span>
                          </span>
                          <span class="mx-2 flex items-center justify-center">
                            <span
                              class="w-4 h-4 border-2 rounded-sm flex justify-center items-center"
                              :class="[
                                currentQuestion?.answer_value.includes(option.value) ? 'border-transparent bg-black' : 'border-gray-400'
                              ]"
                            >
                              <IconCheck
                                class="h-5 w-5 z-50"
                                :stroke-width="currentQuestion?.answer_value.includes(option.value) ? 4 : 1"
                                :class="[
                                  currentQuestion?.answer_value.includes(option.value) ? 'text-white' : 'text-gray-900'
                                ]"
                              />
                            </span>
                          </span>
                        </label>
                      </li>
                    </ul>

                    <div
                      v-if="currentQuestion?.answer_type === 'file'"
                      class="px-4 w-full"
                    >
                      <div class="flex flex-col items-center">
                        <div class="flex flex-col p-5 mt-6 w-full bg-white rounded-3xl border border-solid !border-zinc-300">
                          <div v-if="!currentQuestion?.answer_value">
                            <div class="justify-between py-1">
                              <div class="flex gap-5 max-md:flex-col max-md:gap-0">
                                <div class="flex flex-col w-full max-md:ml-0 max-md:w-full">
                                  <div class="flex grow justify-between items-top text-sm leading-5 text-black">
                                    <div class="grow">
                                      <div
                                        ref="fileDropZoneRef"
                                        class="border-2 border-dashed !border-gray-300 p-4 rounded-lg mb-4 cursor-pointer"
                                        @click="handleFileSelection"
                                      >
                                        <div class="flex flex-col items-center">
                                          <div class="bg-gray-100 w-14 h-14 rounded-full flex items-center justify-center p-2">
                                            <IconCloudUpload class="w-10 h-10" />
                                          </div>
                                          <div class="font-medium mt-3">
                                            Click to Upload or drag and drop
                                          </div>
                                          <div class="text-sm">
                                            (Max. File size: 3 MB)
                                          </div>
                                        </div>
                                      </div>
                                      <input
                                        ref="fileInputRef"
                                        type="file"
                                        name="document_image"
                                        class="hidden h-0 w-0"
                                        @change="onChangeFile"
                                      >
                                      <p class="text-gray-600 text-center text-sm">
                                        {{ currentQuestion.file_error }}
                                      </p>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>

                          <div v-if="currentQuestion?.answer_value">
                            <div class="flex flex-col">
                              <img
                                :src="currentQuestion?.answer_value"
                                alt="id preview"
                                class="object-contain w-full min-h-52 max-h-80 rounded-lg bg-gray-50"
                              >
                              <hr class="my-4">
                              <div class="w-full text-end cols-span-1">
                                <TwButton
                                  class="!p-2 w-full"
                                  variant="secondary"
                                  @click="reupload"
                                >
                                  Reupload
                                </TwButton>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Is Disqualified -->
            <div
              v-if="isAnswerDisqualified"
              class="bg-gray-100 rounded-2xl shadow-sm flex flex-col justify-center items-center p-6"
            >
              <div class="mb-4">
                <IconMoodSad class="size-20" />
              </div>
              <div class="text-3xl text-gray-900 font-semibold mb-3">
                We're sorry
              </div>
              <div class="text-base text-gray-600">
                Your safety is our top priority. Based on your previous answer, you are not a
                candidate to receive treatment for this condition. If you believe you have provided
                incorrect information, please review your answer to the previous question.
              </div>
              <TwButton
                class="mt-8 px-10"
                @click="handleBackClick"
              >
                Return to Previous Question
              </TwButton>
              <RouterLink
                :to="{ name: 'user-subscription' }"
                class="mt-4 text-black underline font-medium"
              >
                End Visit
              </RouterLink>
            </div>
          </div>
        </transition>

        <div
          v-if="!isLoading && !isFinalLoading"
          class="fixed bottom-5 inset-x-1/2"
        >
          <!-- For Factoid -->
          <TwButton
            v-if="currentQuestion?.is_factoid"
            class="w-[300px] shadow-xl transform -translate-x-1/2"
            :loading="isNextBtnDisabled"
            @click="() => {
              nextQuestion()
            }"
          >
            Continue
          </TwButton>
          <!-- For common questions -->
          <TwButton
            v-else-if="currentQuestion?.meta?.is_common_question && isEmpty(currentQuestion?.answer_value)"
            class="w-[300px] shadow-xl transform -translate-x-1/2"
            :loading="isNextBtnDisabled"
            @click="() => {
              currentQuestion.answer_value = 'None'
              isNextBtnDisabled = true
              debouncedValidateQuestions()
            }"
          >
            {{ currentQuestion?.meta?.negative_answer }}
          </TwButton>
          <!-- For all questions -->
          <TwButton
            v-else-if="showNextButton"
            class="w-[300px] shadow-xl transform -translate-x-1/2"
            :loading="isNextBtnDisabled"
            @click="() => {
              isNextBtnDisabled = true
              debouncedValidateQuestions()
            }"
          >
            Continue
          </TwButton>
        </div>
      </div>
    </div>

    <!-- Help modal -->
    <ModalHelp ref="modalHelpRef" />
  </div>
</template>
